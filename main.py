from ctypes import windll, c_int, c_int32, c_voidp, WINFUNCTYPE, wintypes, byref

WH_KEYBOARD_LL = 13
WM_KEYDOWN = 0x0100
WM_KEYUP = 0x0101
WM_SYSKEYDOWN = 0x0104
WM_SYSKEYUP = 0x0105

_HOOKPROC = WINFUNCTYPE(
    wintypes.LPARAM,
    c_int32,
    wintypes.WPARAM,
    wintypes.LPARAM
)
_SetWindowsHookEx = windll.user32.SetWindowsHookExW
_SetWindowsHookEx.argtypes = (
    c_int,
    _HOOKPROC,
    wintypes.HINSTANCE,
    wintypes.DWORD
)
_UnhookWindowsHookEx = windll.user32.UnhookWindowsHookEx
_UnhookWindowsHookEx.argtypes = (
    wintypes.HHOOK,
)
_CallNextHookEx = windll.user32.CallNextHookEx
_CallNextHookEx.argtypes = (
    wintypes.HHOOK,
    c_int,
    wintypes.WPARAM,
    wintypes.LPARAM
)
_GetMessage = windll.user32.GetMessageW
_GetMessage.argtypes = (
    c_voidp,
    wintypes.HWND,
    wintypes.UINT,
    wintypes.UINT
)


@_HOOKPROC
def low_level_keyboard_proc(nCode, wParam, lParam):
    if nCode >= 0:
        if wParam in (WM_KEYDOWN, WM_SYSKEYDOWN):
            print('key down')
        elif wParam in (WM_KEYUP, WM_SYSKEYUP):
            print('key up')
    return _CallNextHookEx(0, nCode, wParam, lParam)


if __name__ == '__main__':
    hook = _SetWindowsHookEx(WH_KEYBOARD_LL, low_level_keyboard_proc, None, 0)
    if not hook:
        print('SetWindowsHookEx failed')
        exit(1)

    print('Hook installed, press keys...')

    msg = wintypes.MSG()
    try:
        while _GetMessage(byref(msg), None, 0, 0) > 0:
            windll.user32.TranslateMessage(byref(msg))
            windll.user32.DispatchMessageW(byref(msg))
    finally:
        _UnhookWindowsHookEx(hook)
        print('Hook removed')
