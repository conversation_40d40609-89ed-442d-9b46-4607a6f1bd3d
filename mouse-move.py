import ctypes
import threading
from time import sleep
from datetime import datetime

MOVE_PERIOD = 2
MOVE_SPEED = 2
FPS = 60
STANDBY_PERIOD = 300
max_dist = MOVE_SPEED * MOVE_PERIOD * FPS


class POINT(ctypes.Structure):
    _fields_ = [("x", ctypes.c_long), ("y", ctypes.c_long)]


def get_mouse_position():
    point = POINT()
    ctypes.windll.user32.GetCursorPos(ctypes.byref(point))
    return point.x, point.y


def reset_timer():
    global count_down
    global mouse_last
    global mouse_curr
    global timer_thread

    if timer_thread is not None:
        timer_thread.cancel()

    mouse_curr = get_mouse_position()
    if mouse_curr[0] != mouse_last[0]:
        count_down = STANDBY_PERIOD
        mouse_last = mouse_curr
        print('moved', timer_thread.native_id)

    if count_down >= 0:
        count_down -= 1

    timer_thread = threading.Timer(1, reset_timer)
    timer_thread.start()


count_down = STANDBY_PERIOD
mouse_last = get_mouse_position()
mouse_curr = mouse_last
timer_thread = None

if __name__ == '__main__':
    print('mouse')
    reset_timer()
    while True:
        if count_down >= 0:
            sleep(0.1)
            continue
        mouse_curr = get_mouse_position()
        if mouse_curr[0] != mouse_last[0]:
            reset_timer()
            continue
        _move = MOVE_SPEED * ((datetime.now().second // MOVE_PERIOD % 2) * 2 - 1)
        if abs(mouse_curr[1] + _move - mouse_last[1]) <= max_dist:
            ctypes.windll.user32.mouse_event(0x0001, 0, _move, 0, 0)
        sleep(1 / FPS)
